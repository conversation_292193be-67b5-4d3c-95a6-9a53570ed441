{"status": "success", "content": [{"endpoint": "/nge/prod/nge-api/api/persons/{personId}/chart/allergies", "content": {"responses": {"400": {"schema": {"$ref": "#/definitions/BadRequest1"}, "examples": {"application/json": {"message": "<string>"}}, "headers": {}, "description": "Bad Request"}, "404": {"headers": {}, "description": "Not Found"}, "403": {"schema": {"$ref": "#/definitions/Forbidden1"}, "examples": {"application/json": {"message": "<string>"}}, "headers": {}, "description": "Forbidden"}, "200": {"schema": {"$ref": "#/definitions/OK"}, "examples": {"application/json": {"Items": [{"reactionDescription": "<string>", "encounterTimestamp": "<string>", "isReadOnly": "<string>", "onsetDate": "<string>", "rxNormDescription": "<string>", "providerId": "<string>", "criticalityCode": "<string>", "personId": "<string>", "resolvedDate": "<string>", "comment": "<string>", "isLocked": "<string>", "allergyId": "<string>", "enterpriseId": "<string>", "encounterTimestampTimezone": "<string>", "id": "<string>", "locationId": "<string>", "allergyType": "<string>", "encounterId": "<string>", "rxNormCode": "<string>", "description": "<string>", "_links": [{"rel": "<string>", "method": "<string>", "description": "<string>", "href": "<string>", "vendorExtensions": "<string>"}, {"rel": "<string>", "description": "<string>", "href": "<string>", "method": "<string>", "vendorExtensions": "<string>"}], "allergyTypeId": "<string>", "practiceId": "<string>"}, {"reactionDescription": "<string>", "encounterTimestamp": "<string>", "isReadOnly": "<string>", "rxNormDescription": "<string>", "onsetDate": "<string>", "providerId": "<string>", "criticalityCode": "<string>", "resolvedDate": "<string>", "personId": "<string>", "comment": "<string>", "enterpriseId": "<string>", "allergyId": "<string>", "isLocked": "<string>", "encounterTimestampTimezone": "<string>", "id": "<string>", "locationId": "<string>", "practiceId": "<string>", "encounterId": "<string>", "rxNormCode": "<string>", "description": "<string>", "_links": [{"rel": "<string>", "href": "<string>", "description": "<string>", "method": "<string>", "vendorExtensions": "<string>"}, {"rel": "<string>", "description": "<string>", "method": "<string>", "href": "<string>", "vendorExtensions": "<string>"}], "allergyTypeId": "<string>", "allergyType": "<string>"}], "NextPageLink": "<string>", "Count": "<string>"}}, "headers": {}, "description": "OK"}}, "operation_id": "{{baseUrl}}/persons/:personId/chart/allergies", "summary": "{{baseUrl}}/persons/:personId/chart/allergies", "consumes": ["application/json"], "produces": ["application/json"], "parameters": [{"in": "query", "name": "$top", "required": true, "description": "", "type": "string"}, {"in": "query", "name": "$filter", "required": true, "description": "", "type": "string"}, {"in": "query", "name": "$orderby", "required": true, "description": "", "type": "string"}, {"name": "$skip", "in": "query", "required": true, "description": "", "type": "string"}, {"in": "query", "name": "$inlinecount", "required": true, "description": "", "type": "string"}, {"name": "$count", "in": "query", "required": true, "description": "", "type": "string"}, {"name": "personId", "in": "path", "required": true, "description": "(Required) (Required) The id of the patient whose allergies are being retrieved", "type": "string"}, {"name": "Accept", "in": "header", "required": true, "description": "", "type": "string"}], "method": "GET", "tags": ["Allergies and Intolerances"], "description": "Returns a list of allergy summaries for the specified person id after applying additional OData query operations.", "path": "/nge/prod/nge-api/api/persons/{personId}/chart/allergies"}, "additional_metadata": {"method": "GET"}}]}